{"source_file": "austrian_file.md", "processing_timestamp": "2025-07-15T09:23:23.928575", "dataset_metadata": {"filepath": "expense_files/austrian_file.png", "filename ": "austrian_file.png", "country": "Austria", "icp": "Global People", "dataset_file": "austrian_file.json"}, "classification_result": {"is_expense": true, "expense_type": "flights", "language": "German", "language_confidence": 95, "document_location": "Austria", "expected_location": "Austria", "location_match": true, "error_type": null, "error_message": null, "classification_confidence": 95, "reasoning": "The document is a flight receipt with detailed flight information including flight numbers, dates, departure and arrival locations. It is consistent with an expense related to flights. The language is German with high confidence, and the document location matches the expected location (Austria)."}, "extraction_result": {"supplier_name": "Austrian Airlines AG", "supplier_address": "Office Park 2, A-1300 Vienna-Airport, Vienna", "vat_number": "ATU15447707", "currency": null, "amount": null, "receipt_type": "Passenger Receipt / Invoice / Rechnung", "receipt_quality": null, "personal_information": "FORISEK / MICHAL DR MR", "business_trip_reporting": null, "travel_template": null, "manager_approval": null, "route_map": null, "kilometer_record": null, "car_details": null, "parking_documentation": null, "billing_address": {"name": "<PERSON><PERSON>", "address": "Lubovnianska 14", "postal_code": "85107", "city": "Bratislava", "country": "Slovakia"}, "booking_code": "6GHMCV", "ticket_number": "257-2133783831", "flight_data": [{"flight_number": "OS561", "date": "2013-08-31", "from": "Vienna Intl", "to": "Zurich", "departure": "07:20 AM", "arrival": "08:45 AM", "class": "Y", "baggage": "1 PC"}, {"flight_number": "OS568", "date": "2013-09-08", "from": "Zurich", "to": "Vienna Intl", "departure": "07:45 AM", "arrival": "09:10 AM", "class": "Y", "baggage": "1 PC"}], "operated_by": "TYROLEAN AIRWAYS"}, "compliance_result": {"validation_result": {"is_valid": false, "issues_count": 3, "issues": [{"issue_type": "Standards & Compliance | Fix Identified", "field": "currency", "description": "Currency field is missing, which is mandatory according to compliance rules for all receipts.", "recommendation": "It is recommended to address this issue with the supplier or provider to ensure currency is recorded on the receipt.", "knowledge_base_reference": "Austria Expense Reimbursement Database requires same currency with clear exchange rate on all ICPs."}, {"issue_type": "Standards & Compliance | Fix Identified", "field": "amount", "description": "Amount field is missing, which is mandatory for expense validation.", "recommendation": "It is recommended to ensure that the amount is clearly stated on the receipt.", "knowledge_base_reference": "Expense Reimbursement rules mandate the amount must be clearly stated on receipt for all ICPs."}, {"issue_type": "Standards & Compliance | Follow-up Action Identified", "field": "business_trip_reporting", "description": "Business trip reporting template usage is required for travel expenses.", "recommendation": "It is necessary to submit a separate report for each trip using the 'Travel Expense Report Template Austria EUR.xlsx'.", "knowledge_base_reference": "Austria corporate travel policy mandates the use of specific templates for all travel receipts."}], "corrected_receipt": null, "compliance_summary": "The receipt is invalid due to missing currency and amount fields. Additionally, adherence to business trip reporting templates is necessary."}, "technical_details": {"content_type": "ReceiptValidationResult", "country": "Austria", "icp": "Global People", "receipt_type": "flights", "issues_count": 3, "has_reasoning": true}}, "processing_status": "completed", "uqlm_validation_available": true}